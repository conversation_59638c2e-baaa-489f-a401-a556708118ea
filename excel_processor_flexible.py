import pandas as pd
import openpyxl
from openpyxl.utils import get_column_letter
from openpyxl.styles import Alignment, Border, Side, Font
import os
import shutil
import sys

def process_excel_with_template(path_windows, muban_path, new_path):
    """
    将源Excel文件的A-H列数据按模板格式复制到新文件中
    
    Args:
        path_windows: 源Excel文件路径
        muban_path: 模板Excel文件路径  
        new_path: 输出Excel文件路径
    
    Returns:
        bool: 处理是否成功
    """
    try:
        # 验证输入文件
        if not os.path.exists(path_windows):
            print(f"错误: 源文件不存在 - {path_windows}")
            return False
            
        if not os.path.exists(muban_path):
            print(f"错误: 模板文件不存在 - {muban_path}")
            return False
        
        # 确保输出目录存在
        output_dir = os.path.dirname(new_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
            print(f"创建输出目录: {output_dir}")
        
        # 复制模板文件到新路径
        shutil.copy2(muban_path, new_path)
        print(f"已复制模板文件到: {new_path}")
        
        # 读取源文件数据
        print(f"正在读取源文件: {path_windows}")
        source_wb = openpyxl.load_workbook(path_windows, data_only=True)
        source_ws = source_wb.active
        
        # 读取A-H列的数据（从第2行开始，跳过第1行）
        data_rows = []
        max_row = source_ws.max_row
        
        print(f"源文件总行数: {max_row}")
        
        for row in range(2, max_row + 1):  # 从第2行开始
            row_data = []
            has_data = False
            
            for col in range(1, 9):  # A-H列 (1-8)
                cell = source_ws.cell(row=row, column=col)
                cell_value = cell.value
                row_data.append(cell_value)
                
                if cell_value is not None and str(cell_value).strip():
                    has_data = True
            
            # 只添加有数据的行
            if has_data:
                data_rows.append(row_data)
        
        print(f"从源文件读取了 {len(data_rows)} 行有效数据")
        
        if not data_rows:
            print("警告: 没有找到有效数据行")
            source_wb.close()
            return True
        
        # 打开新文件（基于模板）
        target_wb = openpyxl.load_workbook(new_path)
        target_ws = target_wb.active
        
        # 检查并处理合并单元格（在填充数据之前）
        print("检查合并单元格...")
        merged_ranges = list(target_ws.merged_cells.ranges)
        ranges_to_unmerge = []

        start_row = 7
        end_row = start_row + len(data_rows) - 1

        for merged_range in merged_ranges:
            # 如果合并单元格与我们要填充的区域重叠
            if (merged_range.max_row >= start_row and
                merged_range.min_row <= end_row and
                merged_range.min_col <= 8 and merged_range.max_col >= 1):  # A-H列

                print(f"发现冲突的合并单元格区域: {merged_range}")
                ranges_to_unmerge.append(merged_range)

        # 取消冲突的合并单元格
        for range_to_unmerge in ranges_to_unmerge:
            target_ws.unmerge_cells(str(range_to_unmerge))
            print(f"已取消合并单元格: {range_to_unmerge}")

        # 从A7开始填充数据
        print(f"开始从第 {start_row} 行填充数据...")

        for i, row_data in enumerate(data_rows):
            current_row = start_row + i

            for col_idx, value in enumerate(row_data):
                col_letter = get_column_letter(col_idx + 1)  # A-H
                cell = target_ws[f"{col_letter}{current_row}"]

                # 设置单元格值
                if value is not None:
                    cell.value = value
                
                # 可选：复制样式（从模板的相应位置）
                try:
                    # 尝试从模板的第6行复制样式（如果存在）
                    template_row = 6
                    if template_row <= target_ws.max_row:
                        template_cell = target_ws[f"{col_letter}{template_row}"]
                        if template_cell.font:
                            cell.font = Font(
                                name=template_cell.font.name or 'Calibri',
                                size=template_cell.font.size or 11,
                                bold=template_cell.font.bold
                            )
                        if template_cell.alignment:
                            cell.alignment = Alignment(
                                horizontal=template_cell.alignment.horizontal,
                                vertical=template_cell.alignment.vertical or 'center'
                            )
                        if template_cell.border:
                            cell.border = template_cell.border
                except Exception as style_error:
                    # 样式复制失败不影响主要功能
                    pass
        

        
        # 保存文件
        target_wb.save(new_path)
        print(f"数据已成功写入: {new_path}")
        print(f"共写入 {len(data_rows)} 行数据，从第 {start_row} 行开始")
        
        # 关闭工作簿
        source_wb.close()
        target_wb.close()
        
        return True
        
    except Exception as e:
        print(f"处理过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """
    主函数 - 可以通过命令行参数或直接设置路径
    """
    # 检查命令行参数
    if len(sys.argv) == 4:
        path_windows = sys.argv[1]
        muban_path = sys.argv[2] 
        new_path = sys.argv[3]
    else:
        # 使用默认路径（请根据实际情况修改）
        base_dir = r"C:\Users\<USER>\Desktop\新建文件夹"
        path_windows = os.path.join(base_dir, "测试数据", "建湖县", "高标准农田项目建设基本底数（二）.xlsx")
        muban_path = os.path.join(base_dir, "模板文件", "区县表格.xlsx")
        new_path = os.path.join(base_dir, "统计成果", "建湖县", "高标准农田项目建设基本底数（二）.xlsx")
    
    print("Excel文件处理程序")
    print("=" * 60)
    print(f"源文件: {path_windows}")
    print(f"模板文件: {muban_path}")
    print(f"输出文件: {new_path}")
    print("=" * 60)
    
    # 执行处理
    success = process_excel_with_template(path_windows, muban_path, new_path)
    
    if success:
        print("\n✓ 处理完成！")
    else:
        print("\n✗ 处理失败！")

if __name__ == "__main__":
    main()
