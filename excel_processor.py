import pandas as pd
import openpyxl
from openpyxl.utils import get_column_letter
from openpyxl.styles import Alignment, Border, Side, Font
import os
import shutil

def process_excel_files(path_windows, muban_path, new_path):
    """
    将源Excel文件的A-H列数据按模板格式复制到新文件中
    
    Args:
        path_windows: 源Excel文件路径
        muban_path: 模板Excel文件路径  
        new_path: 输出Excel文件路径
    """
    try:
        # 确保输出目录存在
        output_dir = os.path.dirname(new_path)
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 复制模板文件到新路径
        shutil.copy2(muban_path, new_path)
        print(f"已复制模板文件到: {new_path}")
        
        # 读取源文件数据
        print(f"正在读取源文件: {path_windows}")
        source_wb = openpyxl.load_workbook(path_windows)
        source_ws = source_wb.active
        
        # 读取A-H列的数据（从第2行开始，跳过第1行）
        data_rows = []
        max_row = source_ws.max_row
        
        for row in range(2, max_row + 1):  # 从第2行开始
            row_data = []
            for col in range(1, 9):  # A-H列 (1-8)
                cell_value = source_ws.cell(row=row, column=col).value
                row_data.append(cell_value)
            
            # 检查是否为空行（所有单元格都为空）
            if any(cell is not None for cell in row_data):
                data_rows.append(row_data)
        
        print(f"从源文件读取了 {len(data_rows)} 行数据")
        
        # 打开新文件（基于模板）
        target_wb = openpyxl.load_workbook(new_path)
        target_ws = target_wb.active
        
        # 从A7开始填充数据
        start_row = 7
        
        for i, row_data in enumerate(data_rows):
            current_row = start_row + i
            
            for col_idx, value in enumerate(row_data):
                col_letter = get_column_letter(col_idx + 1)  # A-H
                cell = target_ws[f"{col_letter}{current_row}"]
                cell.value = value
                
                # 复制基本样式（可选）
                if current_row == start_row:  # 第一行数据，可以参考模板样式
                    template_cell = target_ws[f"{col_letter}{start_row-1}"]
                    if template_cell.font:
                        cell.font = Font(
                            name=template_cell.font.name,
                            size=template_cell.font.size,
                            bold=template_cell.font.bold
                        )
                    if template_cell.alignment:
                        cell.alignment = Alignment(
                            horizontal=template_cell.alignment.horizontal,
                            vertical=template_cell.alignment.vertical
                        )
        
        # 处理合并单元格的情况
        # 检查模板中A8-H8是否有合并单元格，如果有则相应调整
        merged_ranges = list(target_ws.merged_cells.ranges)
        for merged_range in merged_ranges:
            # 如果合并单元格在我们要填充的区域，需要取消合并或调整
            if merged_range.min_row >= start_row:
                print(f"发现合并单元格区域: {merged_range}")
                # 这里可以根据需要处理合并单元格
                # 例如：target_ws.unmerge_cells(str(merged_range))
        
        # 保存文件
        target_wb.save(new_path)
        print(f"数据已成功写入: {new_path}")
        print(f"共写入 {len(data_rows)} 行数据，从第 {start_row} 行开始")
        
        # 关闭工作簿
        source_wb.close()
        target_wb.close()
        
        return True
        
    except Exception as e:
        print(f"处理过程中出现错误: {str(e)}")
        return False

def main():
    """
    主函数 - 设置文件路径并执行处理
    """
    # 示例路径（请根据实际情况修改）
    path_windows = r"C:\Users\<USER>\Desktop\新建文件夹\测试数据\建湖县\高标准农田项目建设基本底数（二）.xlsx"
    muban_path = r"C:\Users\<USER>\Desktop\新建文件夹\模板文件\区县表格.xlsx"
    new_path = r"C:\Users\<USER>\Desktop\新建文件夹\统计成果\建湖县\高标准农田项目建设基本底数（二）.xlsx"
    
    print("Excel文件处理程序")
    print("=" * 50)
    print(f"源文件: {path_windows}")
    print(f"模板文件: {muban_path}")
    print(f"输出文件: {new_path}")
    print("=" * 50)
    
    # 检查源文件是否存在
    if not os.path.exists(path_windows):
        print(f"错误: 源文件不存在 - {path_windows}")
        return
    
    # 检查模板文件是否存在
    if not os.path.exists(muban_path):
        print(f"错误: 模板文件不存在 - {muban_path}")
        return
    
    # 执行处理
    success = process_excel_files(path_windows, muban_path, new_path)
    
    if success:
        print("\n处理完成！")
    else:
        print("\n处理失败！")

if __name__ == "__main__":
    main()
