"""
Excel数据处理工具 - FME集成版本

功能：将源Excel文件的A-I列数据（除第一行）按模板格式复制到新文件中
支持区县算法和乡镇算法

FME参数：
- a: 算法选择（1=区县算法, 2=乡镇算法）
- path_windows: 源Excel文件路径
- new_path: 输出Excel文件路径
- 模板_path: 模板Excel文件路径
"""

import fme 
import fmeobjects
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils import get_column_letter
import os
import shutil


class FeatureProcessor(object):
    """Excel处理工具的FME集成版本
    支持区县算法和乡镇算法的Excel数据处理
    """

    def __init__(self):
        """Base constructor for class members."""
        pass
        
    def has_support_for(self, support_type: int):
        """This method is called by FME to determine if the PythonCaller supports Bulk mode,
        which allows for significant performance gains when processing large numbers of features.
        Bulk mode cannot always be supported. 
        More information available in transformer help.
        """
        return support_type == fmeobjects.FME_SUPPORT_FEATURE_TABLE_SHIM
  
    def input(self, feature: fmeobjects.FMEFeature):
        """处理每个输入的FME Feature
        从feature中获取参数并执行Excel处理
        """
        try:
            # 从feature获取参数
            a = int(feature.getAttribute('a'))  # 算法选择：1=区县算法, 2=乡镇算法
            path_windows = feature.getAttribute('path_windows')  # 源文件路径
            new_path = feature.getAttribute('new_path')  # 输出文件路径
            muban_path = feature.getAttribute('模板_path')  # 模板文件路径
            
            # 验证参数
            if not all([path_windows, new_path, muban_path]):
                feature.setAttribute('处理状态', '失败')
                feature.setAttribute('错误信息', '缺少必要的路径参数')
                self.pyoutput(feature)
                return
            
            if a not in [1, 2]:
                feature.setAttribute('处理状态', '失败')
                feature.setAttribute('错误信息', f'无效的算法选择: a={a}')
                self.pyoutput(feature)
                return
            
            # 执行Excel处理
            if a == 1:
                result = self.process_county_algorithm(path_windows, muban_path, new_path)
            elif a == 2:
                result = self.process_township_algorithm(path_windows, muban_path, new_path)
            
            # 设置处理结果到feature
            if result['success']:
                feature.setAttribute('处理状态', '成功')
                feature.setAttribute('处理行数', result['rows_processed'])
                feature.setAttribute('输出文件', new_path)
                feature.setAttribute('算法类型', '区县算法' if a == 1 else '乡镇算法')
            else:
                feature.setAttribute('处理状态', '失败')
                feature.setAttribute('错误信息', result['message'])
            
        except Exception as e:
            feature.setAttribute('处理状态', '失败')
            feature.setAttribute('错误信息', f'处理异常: {str(e)}')
        
        self.pyoutput(feature)

    def close(self):
        """This method is called once all the FME Features have been processed from input()."""
        pass

    def process_group(self):
        """This method is called by FME for each group when group processing mode is enabled."""
        pass

    def process_county_algorithm(self, source_path, template_path, output_path):
        """区县算法：处理单个工作表"""
        try:
            # 验证输入文件
            if not os.path.exists(source_path):
                return {'success': False, 'message': f"源文件不存在: {source_path}", 'rows_processed': 0}
                
            if not os.path.exists(template_path):
                return {'success': False, 'message': f"模板文件不存在: {template_path}", 'rows_processed': 0}
            
            # 确保输出目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            # 复制模板文件到新路径
            shutil.copy2(template_path, output_path)
            
            # 读取源文件数据
            source_wb = openpyxl.load_workbook(source_path, data_only=True)
            source_ws = source_wb.active
            
            # 读取A-I列的数据（从第2行开始，跳过第1行）
            data_rows = []
            max_row = source_ws.max_row
            
            for row in range(2, max_row + 1):  # 从第2行开始
                row_data = []
                has_data = False
                
                for col in range(1, 10):  # A-I列 (1-9)
                    cell = source_ws.cell(row=row, column=col)
                    cell_value = cell.value
                    row_data.append(cell_value)
                    
                    if cell_value is not None and str(cell_value).strip():
                        has_data = True
                
                # 只添加有数据的行
                if has_data:
                    data_rows.append(row_data)
            
            if not data_rows:
                source_wb.close()
                return {'success': False, 'message': '源文件中没有找到有效数据', 'rows_processed': 0}
            
            # 创建市级代码到名称的映射
            city_code_map = {
                '3201': '南京市', '3202': '无锡市', '3203': '徐州市', '3204': '常州市',
                '3205': '苏州市', '3206': '南通市', '3207': '连云港市', '3208': '淮安市',
                '3209': '盐城市', '3210': '扬州市', '3211': '镇江市', '3212': '泰州市',
                '3213': '宿迁市'
            }
            
            # 获取区县名称和代码
            county_name = "建湖县"  # 默认值
            city_name = "盐城市"    # 默认值
            
            # 尝试从数据中提取市县信息
            if data_rows:
                first_row = data_rows[0]
                if len(first_row) > 1 and first_row[1]:
                    town_name = str(first_row[1])
                    # 从乡镇名推断县名
                    if '建湖' in town_name or '芦沟' in town_name or '庆丰' in town_name:
                        county_name = "建湖县"
                        city_name = "盐城市"
            
            # 打开目标文件
            target_wb = openpyxl.load_workbook(output_path)
            target_ws = target_wb.active
            
            # 设置工作表名称
            source_sheet_name = source_ws.title
            target_ws.title = source_sheet_name
            
            # 修改A1标题
            new_title = f"{city_name}{county_name}2019-2025年高标准农田建设基本底数（二）"
            target_ws['A1'].value = new_title
            
            # 保存A7行的内容
            start_row = 7
            a7_content = None
            a7_merged_range = None
            
            # 查找A7的合并单元格
            for merged_range in target_ws.merged_cells.ranges:
                if (merged_range.min_row == start_row and merged_range.min_col == 1):
                    a7_merged_range = merged_range
                    a7_cell = target_ws.cell(row=start_row, column=1)
                    a7_content = a7_cell.value
                    target_ws.unmerge_cells(str(merged_range))
                    
                    # 清除A7行的内容和格式
                    for col in range(1, 35):  # A到AH列
                        cell = target_ws.cell(row=start_row, column=col)
                        cell.value = None
                        cell.font = Font()
                        cell.alignment = Alignment()
                    break
            
            # 检查其他合并单元格
            merged_ranges_to_remove = []
            for merged_range in target_ws.merged_cells.ranges:
                if merged_range.min_row >= start_row and merged_range.min_row < start_row + len(data_rows):
                    merged_ranges_to_remove.append(merged_range)
            
            for merged_range in merged_ranges_to_remove:
                target_ws.unmerge_cells(str(merged_range))
            
            # 创建样式
            fill_color = PatternFill(start_color="E2EFDA", end_color="E2EFDA", fill_type="solid")
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
            
            # 从A7开始填充数据
            for i, row_data in enumerate(data_rows):
                current_row = start_row + i
                target_ws.row_dimensions[current_row].height = 20
                
                # 处理A-AH列
                for col_idx in range(1, 35):  # A到AH列
                    col_letter = get_column_letter(col_idx)
                    cell = target_ws[f"{col_letter}{current_row}"]
                    
                    # 如果是A-I列，设置数据值
                    if col_idx <= 9 and col_idx - 1 < len(row_data):
                        value = row_data[col_idx - 1]
                        if value is not None:
                            cell.value = value
                    
                    # 为所有A-AH列设置统一格式
                    try:
                        # 设置标准的数据格式
                        cell.font = Font(
                            name='宋体',
                            size=11,
                            bold=False
                        )
                        cell.alignment = Alignment(
                            horizontal='center',
                            vertical='center',
                            wrap_text=False
                        )
                        
                        # 设置填充色
                        cell.fill = fill_color
                        
                        # 设置内外边框
                        cell.border = thin_border
                        
                    except Exception as format_error:
                        pass  # 忽略格式设置错误
            
            # 计算第6行的汇总值
            def safe_sum_column(col_letter):
                total = 0
                col_index = ord(col_letter) - ord('A')
                for row_data in data_rows:
                    if col_index < len(row_data):
                        value = row_data[col_index]
                        if value is not None:
                            try:
                                if isinstance(value, str):
                                    cleaned_value = value.replace(',', '').replace(' ', '')
                                    numeric_value = float(cleaned_value) if cleaned_value else 0
                                else:
                                    numeric_value = float(value)
                                total += numeric_value
                            except (ValueError, TypeError):
                                pass
                return total
            
            # 设置第6行的值
            target_ws['B6'].value = len(data_rows)  # 数据条数
            target_ws['E6'].value = safe_sum_column('E')  # E列求和
            target_ws['F6'].value = safe_sum_column('F')  # F列求和
            target_ws['G6'].value = safe_sum_column('G')  # G列求和
            target_ws['I6'].value = safe_sum_column('I')  # I列求和
            
            # H6 = G6/F6*100
            f6_value = safe_sum_column('F')
            g6_value = safe_sum_column('G')
            if f6_value != 0:
                h6_value = round((g6_value / f6_value) * 100, 1)
            else:
                h6_value = 0.0
            target_ws['H6'].value = h6_value
            
            # 为第6行设置加粗格式
            for col_idx in range(1, 35):  # A到AH列
                col_letter = get_column_letter(col_idx)
                cell = target_ws[f"{col_letter}6"]
                current_font = cell.font
                cell.font = Font(
                    name=current_font.name or '宋体',
                    size=current_font.size or 11,
                    bold=True,
                    italic=current_font.italic,
                    underline=current_font.underline,
                    color=current_font.color
                )
            
            # 将A7的内容放到数据的最后一行
            if a7_content is not None:
                final_row = start_row + len(data_rows)
                if a7_merged_range:
                    new_range = f"A{final_row}:AH{final_row}"
                    target_ws.merge_cells(new_range)
                    target_ws[f"A{final_row}"].value = a7_content
                    
                    # 设置格式
                    try:
                        new_cell = target_ws[f"A{final_row}"]
                        new_cell.font = Font(name='宋体', size=11, bold=True)
                        new_cell.alignment = Alignment(horizontal='left', vertical='top', wrap_text=True)
                        target_ws.row_dimensions[final_row].height = 77
                    except:
                        pass
            
            # 保存文件
            target_wb.save(output_path)
            target_wb.close()
            source_wb.close()
            
            return {
                'success': True,
                'message': '区县算法处理成功',
                'rows_processed': len(data_rows)
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f"区县算法处理失败: {e}",
                'rows_processed': 0
            }

    def process_township_algorithm(self, source_path, template_path, output_path):
        """乡镇算法：处理多个工作表"""
        try:
            # 验证输入文件
            if not os.path.exists(source_path):
                return {'success': False, 'message': f"源文件不存在: {source_path}", 'rows_processed': 0}

            if not os.path.exists(template_path):
                return {'success': False, 'message': f"模板文件不存在: {template_path}", 'rows_processed': 0}

            source_wb = openpyxl.load_workbook(source_path, data_only=True)

            # 检查输出文件是否存在
            if os.path.exists(output_path):
                target_wb = openpyxl.load_workbook(output_path)
            else:
                # 创建输出目录
                output_dir = os.path.dirname(output_path)
                if output_dir and not os.path.exists(output_dir):
                    os.makedirs(output_dir)

                # 复制模板文件到输出位置
                shutil.copy2(template_path, output_path)
                target_wb = openpyxl.load_workbook(output_path)

            total_processed = 0

            # 处理源文件中的每个工作表
            for sheet_name in source_wb.sheetnames:
                source_ws = source_wb[sheet_name]

                # 检查目标文件中是否已存在同名工作表
                if sheet_name in target_wb.sheetnames:
                    continue

                # 创建新的工作表（复制模板的第一个工作表）
                template_ws = target_wb.worksheets[0]
                new_ws = target_wb.copy_worksheet(template_ws)
                new_ws.title = sheet_name

                # 处理这个工作表的数据
                rows_processed = self.process_single_township_sheet(source_ws, new_ws, sheet_name)
                total_processed += rows_processed

            # 删除原始的模板工作表（如果存在且不是我们需要的）
            if len(target_wb.worksheets) > 1:
                template_sheet = target_wb.worksheets[0]
                if template_sheet.title not in source_wb.sheetnames:
                    target_wb.remove(template_sheet)

            # 保存文件
            target_wb.save(output_path)
            target_wb.close()
            source_wb.close()

            return {
                'success': True,
                'message': '乡镇算法处理成功',
                'rows_processed': total_processed
            }

        except Exception as e:
            return {
                'success': False,
                'message': f"乡镇算法处理失败: {e}",
                'rows_processed': 0
            }

    def process_single_township_sheet(self, source_ws, target_ws, township_name):
        """处理单个乡镇工作表"""
        # 读取A-I列的数据（从第2行开始，跳过第1行）
        data_rows = []
        max_row = source_ws.max_row

        for row in range(2, max_row + 1):  # 从第2行开始
            row_data = []
            has_data = False

            for col in range(1, 10):  # A-I列 (1-9)
                cell = source_ws.cell(row=row, column=col)
                cell_value = cell.value
                row_data.append(cell_value)

                if cell_value is not None and str(cell_value).strip():
                    has_data = True

            # 只添加有数据的行
            if has_data:
                data_rows.append(row_data)

        if not data_rows:
            return 0

        # 获取区县名称和代码
        county_name = "建湖县"  # 默认值
        city_name = "盐城市"    # 默认值

        # 修改A1标题（包含乡镇名称）
        new_title = f"{city_name}{county_name}{township_name}2019-2025年高标准农田建设基本底数（二）"
        target_ws['A1'].value = new_title

        # 保存A7行的内容
        start_row = 7
        a7_content = None
        a7_merged_range = None

        # 查找A7的合并单元格
        for merged_range in target_ws.merged_cells.ranges:
            if (merged_range.min_row == start_row and merged_range.min_col == 1):
                a7_merged_range = merged_range
                a7_cell = target_ws.cell(row=start_row, column=1)
                a7_content = a7_cell.value
                target_ws.unmerge_cells(str(merged_range))

                # 清除A7行的内容和格式
                for col in range(1, 35):  # A到AH列
                    cell = target_ws.cell(row=start_row, column=col)
                    cell.value = None
                    cell.font = Font()
                    cell.alignment = Alignment()
                break

        # 检查其他合并单元格
        merged_ranges_to_remove = []
        for merged_range in target_ws.merged_cells.ranges:
            if merged_range.min_row >= start_row and merged_range.min_row < start_row + len(data_rows):
                merged_ranges_to_remove.append(merged_range)

        for merged_range in merged_ranges_to_remove:
            target_ws.unmerge_cells(str(merged_range))

        # 从A7开始填充数据（乡镇算法不需要填充色，但需要边框）
        # 创建边框样式
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        for i, row_data in enumerate(data_rows):
            current_row = start_row + i
            target_ws.row_dimensions[current_row].height = 20

            # 处理A-AH列
            for col_idx in range(1, 35):  # A到AH列
                col_letter = get_column_letter(col_idx)
                cell = target_ws[f"{col_letter}{current_row}"]

                # 如果是A-I列，设置数据值
                if col_idx <= 9 and col_idx - 1 < len(row_data):
                    value = row_data[col_idx - 1]
                    if value is not None:
                        cell.value = value

                # 设置标准格式（乡镇算法不需要填充色，但需要边框）
                try:
                    cell.font = Font(name='宋体', size=11, bold=False)
                    cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=False)
                    # 不设置填充色，但设置边框
                    cell.border = thin_border
                except:
                    pass

        # 计算第6行的汇总值
        def safe_sum_column(col_letter):
            total = 0
            col_index = ord(col_letter) - ord('A')
            for row_data in data_rows:
                if col_index < len(row_data):
                    value = row_data[col_index]
                    if value is not None:
                        try:
                            if isinstance(value, str):
                                cleaned_value = value.replace(',', '').replace(' ', '')
                                numeric_value = float(cleaned_value) if cleaned_value else 0
                            else:
                                numeric_value = float(value)
                            total += numeric_value
                        except (ValueError, TypeError):
                            pass
            return total

        # 设置第6行的值
        target_ws['B6'].value = len(data_rows)  # 数据条数
        target_ws['E6'].value = safe_sum_column('E')  # E列求和
        target_ws['F6'].value = safe_sum_column('F')  # F列求和
        target_ws['G6'].value = safe_sum_column('G')  # G列求和
        target_ws['I6'].value = safe_sum_column('I')  # I列求和

        # H6 = G6/F6*100
        f6_value = safe_sum_column('F')
        g6_value = safe_sum_column('G')
        if f6_value != 0:
            h6_value = round((g6_value / f6_value) * 100, 1)
        else:
            h6_value = 0.0
        target_ws['H6'].value = h6_value

        # 为第6行设置加粗格式
        for col_idx in range(1, 35):  # A到AH列
            col_letter = get_column_letter(col_idx)
            cell = target_ws[f"{col_letter}6"]
            current_font = cell.font
            cell.font = Font(
                name=current_font.name or '宋体',
                size=current_font.size or 11,
                bold=True,
                italic=current_font.italic,
                underline=current_font.underline,
                color=current_font.color
            )

        # 将A7的内容放到数据的最后一行
        if a7_content is not None:
            final_row = start_row + len(data_rows)
            if a7_merged_range:
                new_range = f"A{final_row}:AH{final_row}"
                target_ws.merge_cells(new_range)
                target_ws[f"A{final_row}"].value = a7_content

                # 设置格式
                try:
                    new_cell = target_ws[f"A{final_row}"]
                    new_cell.font = Font(name='宋体', size=11, bold=True)
                    new_cell.alignment = Alignment(horizontal='left', vertical='top', wrap_text=True)
                    target_ws.row_dimensions[final_row].height = 77
                except:
                    pass

        return len(data_rows)
