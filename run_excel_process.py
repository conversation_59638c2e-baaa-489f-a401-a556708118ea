"""
Excel文件处理脚本
用于将源Excel文件的A-H列数据按模板格式复制到新文件中

使用方法：
1. 修改下面的三个路径变量
2. 运行脚本
"""

from excel_processor_flexible import process_excel_with_template
import os

def main():
    # ===== 请修改以下三个路径变量 =====
    
    # 源Excel文件路径（包含要复制的数据）
    path_windows = r"C:\Users\<USER>\Desktop\新建文件夹\测试数据\建湖县\高标准农田项目建设基本底数（二）.xlsx"
    
    # 模板Excel文件路径
    muban_path = r"C:\Users\<USER>\Desktop\新建文件夹\模板文件\区县表格.xlsx"
    
    # 输出Excel文件路径（文件和文件夹可能均不存在，会自动创建）
    new_path = r"C:\Users\<USER>\Desktop\新建文件夹\统计成果\建湖县\高标准农田项目建设基本底数（二）.xlsx"
    
    # ===== 路径设置结束 =====
    
    print("Excel数据处理工具")
    print("=" * 50)
    print("功能：将源文件A-H列数据（除第一行）复制到模板文件中")
    print("起始位置：A7")
    print("=" * 50)
    
    print(f"源文件路径：{path_windows}")
    print(f"模板路径：{muban_path}")
    print(f"输出路径：{new_path}")
    print()
    
    # 检查文件是否存在
    if not os.path.exists(path_windows):
        print(f"❌ 错误：源文件不存在")
        print(f"   请检查路径：{path_windows}")
        return
    
    if not os.path.exists(muban_path):
        print(f"❌ 错误：模板文件不存在")
        print(f"   请检查路径：{muban_path}")
        return
    
    print("✓ 文件检查通过，开始处理...")
    print()
    
    # 执行处理
    success = process_excel_with_template(path_windows, muban_path, new_path)
    
    if success:
        print()
        print("🎉 处理成功完成！")
        print(f"📁 输出文件已保存到：{new_path}")
    else:
        print()
        print("❌ 处理失败，请检查错误信息")

if __name__ == "__main__":
    main()
