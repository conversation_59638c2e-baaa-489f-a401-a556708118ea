"""
Excel处理工具函数
提供简单易用的函数来处理Excel文件数据复制
"""

import openpyxl
from openpyxl.utils import get_column_letter
from openpyxl.styles import Alignment, Font
import os
import shutil

def copy_excel_data_with_template(path_windows, muban_path, new_path, start_row=7, columns='A:H'):
    """
    将源Excel文件的指定列数据按模板格式复制到新文件中
    
    Args:
        path_windows (str): 源Excel文件路径
        muban_path (str): 模板Excel文件路径  
        new_path (str): 输出Excel文件路径
        start_row (int): 开始填充数据的行号，默认为7
        columns (str): 要复制的列范围，默认为'A:H'
    
    Returns:
        dict: 包含处理结果的字典
            - success (bool): 是否成功
            - message (str): 处理信息
            - rows_processed (int): 处理的行数
    """
    
    result = {
        'success': False,
        'message': '',
        'rows_processed': 0
    }
    
    try:
        # 验证输入文件
        if not os.path.exists(path_windows):
            result['message'] = f"源文件不存在: {path_windows}"
            return result
            
        if not os.path.exists(muban_path):
            result['message'] = f"模板文件不存在: {muban_path}"
            return result
        
        # 解析列范围
        if ':' in columns:
            start_col, end_col = columns.split(':')
            start_col_idx = ord(start_col.upper()) - ord('A') + 1
            end_col_idx = ord(end_col.upper()) - ord('A') + 1
        else:
            result['message'] = f"列范围格式错误: {columns}，应为 'A:H' 格式"
            return result
        
        # 确保输出目录存在
        output_dir = os.path.dirname(new_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 复制模板文件到新路径
        shutil.copy2(muban_path, new_path)
        
        # 读取源文件数据
        source_wb = openpyxl.load_workbook(path_windows, data_only=True)
        source_ws = source_wb.active
        
        # 读取指定列的数据（从第2行开始，跳过第1行）
        data_rows = []
        max_row = source_ws.max_row
        
        for row in range(2, max_row + 1):  # 从第2行开始
            row_data = []
            has_data = False
            
            for col in range(start_col_idx, end_col_idx + 1):
                cell = source_ws.cell(row=row, column=col)
                cell_value = cell.value
                row_data.append(cell_value)
                
                if cell_value is not None and str(cell_value).strip():
                    has_data = True
            
            # 只添加有数据的行
            if has_data:
                data_rows.append(row_data)
        
        if not data_rows:
            result['message'] = "没有找到有效数据行"
            result['success'] = True
            source_wb.close()
            return result
        
        # 打开新文件（基于模板）
        target_wb = openpyxl.load_workbook(new_path)
        target_ws = target_wb.active
        
        # 检查并处理合并单元格（在填充数据之前）
        merged_ranges = list(target_ws.merged_cells.ranges)
        ranges_to_unmerge = []
        
        end_row = start_row + len(data_rows) - 1
        
        for merged_range in merged_ranges:
            # 如果合并单元格与我们要填充的区域重叠
            if (merged_range.max_row >= start_row and 
                merged_range.min_row <= end_row and
                merged_range.min_col <= end_col_idx and 
                merged_range.max_col >= start_col_idx):
                
                ranges_to_unmerge.append(merged_range)
        
        # 取消冲突的合并单元格
        for range_to_unmerge in ranges_to_unmerge:
            target_ws.unmerge_cells(str(range_to_unmerge))
        
        # 填充数据
        for i, row_data in enumerate(data_rows):
            current_row = start_row + i
            
            for col_idx, value in enumerate(row_data):
                col_letter = get_column_letter(start_col_idx + col_idx)
                cell = target_ws[f"{col_letter}{current_row}"]
                
                # 设置单元格值
                if value is not None:
                    cell.value = value
                
                # 可选：复制样式
                try:
                    template_row = start_row - 1
                    if template_row > 0:
                        template_cell = target_ws[f"{col_letter}{template_row}"]
                        if template_cell.font:
                            cell.font = Font(
                                name=template_cell.font.name or 'Calibri',
                                size=template_cell.font.size or 11,
                                bold=template_cell.font.bold
                            )
                        if template_cell.alignment:
                            cell.alignment = Alignment(
                                horizontal=template_cell.alignment.horizontal,
                                vertical=template_cell.alignment.vertical or 'center'
                            )
                        if template_cell.border:
                            cell.border = template_cell.border
                except:
                    pass
        
        # 保存文件
        target_wb.save(new_path)
        
        # 关闭工作簿
        source_wb.close()
        target_wb.close()
        
        result['success'] = True
        result['message'] = f"成功处理 {len(data_rows)} 行数据"
        result['rows_processed'] = len(data_rows)
        
        return result
        
    except Exception as e:
        result['message'] = f"处理过程中出现错误: {str(e)}"
        return result

# 使用示例
if __name__ == "__main__":
    # 示例用法
    path_windows = r"C:\Users\<USER>\Desktop\新建文件夹\测试数据\建湖县\高标准农田项目建设基本底数（二）.xlsx"
    muban_path = r"C:\Users\<USER>\Desktop\新建文件夹\模板文件\区县表格.xlsx"
    new_path = r"C:\Users\<USER>\Desktop\新建文件夹\统计成果\建湖县\高标准农田项目建设基本底数（二）_test.xlsx"
    
    result = copy_excel_data_with_template(path_windows, muban_path, new_path)
    
    if result['success']:
        print(f"✓ {result['message']}")
        print(f"📁 文件已保存到: {new_path}")
    else:
        print(f"✗ {result['message']}")
