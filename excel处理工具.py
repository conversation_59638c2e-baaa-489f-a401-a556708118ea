"""
Excel数据处理工具 - 完整版本

功能：将源Excel文件的A-H列数据（除第一行）按模板格式复制到新文件中
起始位置：A7行，自动处理合并单元格

使用方法：
1. 修改下面三个路径变量
2. 运行脚本
"""

import openpyxl
from openpyxl.utils import get_column_letter
from openpyxl.styles import Alignment, Font
import os
import shutil

# ==================== 请修改这三个路径 ====================

# 变量path_windows：源Excel文件路径（包含要复制的数据）
path_windows = r"C:\Users\<USER>\Desktop\新建文件夹\测试数据\建湖县\高标准农田项目建设基本底数（二）.xlsx"

# 变量muban_path：模板Excel文件路径
muban_path = r"C:\Users\<USER>\Desktop\新建文件夹\模板文件\区县表格.xlsx"

# 变量new_path：输出Excel文件路径（文件和文件夹可能均不存在，会自动创建）
new_path = r"C:\Users\<USER>\Desktop\新建文件夹\统计成果\建湖县\高标准农田项目建设基本底数（二）.xlsx"

# ==================== 路径设置结束 ====================

def process_excel_files(source_path, template_path, output_path):
    """
    处理Excel文件的主函数
    
    Args:
        source_path: 源Excel文件路径
        template_path: 模板Excel文件路径  
        output_path: 输出Excel文件路径
    
    Returns:
        dict: 包含处理结果的字典
    """
    
    result = {
        'success': False,
        'message': '',
        'rows_processed': 0
    }
    
    try:
        # 验证输入文件
        if not os.path.exists(source_path):
            result['message'] = f"源文件不存在: {source_path}"
            return result
            
        if not os.path.exists(template_path):
            result['message'] = f"模板文件不存在: {template_path}"
            return result
        
        # 确保输出目录存在
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
            print(f"✓ 创建输出目录: {output_dir}")
        
        # 复制模板文件到新路径
        shutil.copy2(template_path, output_path)
        print(f"✓ 已复制模板文件到: {output_path}")
        
        # 读取源文件数据
        print(f"📖 正在读取源文件: {os.path.basename(source_path)}")
        source_wb = openpyxl.load_workbook(source_path, data_only=True)
        source_ws = source_wb.active
        
        # 读取A-H列的数据（从第2行开始，跳过第1行）
        data_rows = []
        max_row = source_ws.max_row
        
        print(f"📊 源文件总行数: {max_row}")
        
        for row in range(2, max_row + 1):  # 从第2行开始
            row_data = []
            has_data = False
            
            for col in range(1, 9):  # A-H列 (1-8)
                cell = source_ws.cell(row=row, column=col)
                cell_value = cell.value
                row_data.append(cell_value)
                
                if cell_value is not None and str(cell_value).strip():
                    has_data = True
            
            # 只添加有数据的行
            if has_data:
                data_rows.append(row_data)
        
        print(f"📈 从源文件读取了 {len(data_rows)} 行有效数据")
        
        if not data_rows:
            result['message'] = "没有找到有效数据行"
            result['success'] = True
            source_wb.close()
            return result
        
        # 打开新文件（基于模板）
        target_wb = openpyxl.load_workbook(output_path)
        target_ws = target_wb.active

        # 保存A7行的内容（A-AH的合并单元格内容）
        print("💾 保存A7行的提示内容...")
        start_row = 7
        a7_content = None
        a7_merged_range = None

        # 查找A7的合并单元格
        for merged_range in target_ws.merged_cells.ranges:
            if (merged_range.min_row == start_row and
                merged_range.min_col == 1):  # A7开始的合并单元格
                a7_merged_range = merged_range
                # 获取合并单元格的内容
                a7_content = target_ws.cell(row=start_row, column=1).value
                print(f"📝 找到A7合并单元格: {merged_range}, 内容: {a7_content}")
                # 取消这个合并单元格
                target_ws.unmerge_cells(str(merged_range))
                print(f"✂️  临时取消A7合并单元格")
                break

        # 检查并处理其他可能冲突的合并单元格
        print("🔍 检查其他合并单元格...")
        merged_ranges = list(target_ws.merged_cells.ranges)
        ranges_to_unmerge = []

        end_row = start_row + len(data_rows) - 1

        for merged_range in merged_ranges:
            # 如果合并单元格与我们要填充的区域重叠（排除已处理的A7）
            if (merged_range.max_row >= start_row and
                merged_range.min_row <= end_row and
                merged_range.min_col <= 8 and
                merged_range.max_col >= 1):  # A-H列

                print(f"⚠️  发现冲突的合并单元格区域: {merged_range}")
                ranges_to_unmerge.append(merged_range)

        # 取消冲突的合并单元格
        for range_to_unmerge in ranges_to_unmerge:
            target_ws.unmerge_cells(str(range_to_unmerge))
            print(f"✂️  已取消合并单元格: {range_to_unmerge}")
        
        # 从A7开始填充数据
        print(f"📝 开始从第 {start_row} 行填充数据...")
        
        for i, row_data in enumerate(data_rows):
            current_row = start_row + i
            
            for col_idx, value in enumerate(row_data):
                col_letter = get_column_letter(col_idx + 1)  # A-H
                cell = target_ws[f"{col_letter}{current_row}"]
                
                # 设置单元格值
                if value is not None:
                    cell.value = value
                
                # 可选：复制样式（从模板的相应位置）
                try:
                    template_row = start_row - 1
                    if template_row > 0:
                        template_cell = target_ws[f"{col_letter}{template_row}"]
                        if template_cell.font:
                            cell.font = Font(
                                name=template_cell.font.name or 'Calibri',
                                size=template_cell.font.size or 11,
                                bold=template_cell.font.bold
                            )
                        if template_cell.alignment:
                            cell.alignment = Alignment(
                                horizontal=template_cell.alignment.horizontal,
                                vertical=template_cell.alignment.vertical or 'center'
                            )
                        if template_cell.border:
                            cell.border = template_cell.border
                except:
                    # 样式复制失败不影响主要功能
                    pass
        
        # 将A7的内容放到数据的最后一行
        if a7_content is not None:
            final_row = start_row + len(data_rows)
            print(f"📌 将A7的提示内容移动到第 {final_row} 行...")

            # 在最后一行重新创建合并单元格并设置内容
            if a7_merged_range:
                # 计算新的合并范围（保持相同的列范围，但移动到最后）
                new_range = f"A{final_row}:AH{final_row}"
                target_ws.merge_cells(new_range)
                target_ws[f"A{final_row}"].value = a7_content
                print(f"✅ 已在第 {final_row} 行重新创建合并单元格: {new_range}")

                # 复制原有样式（如果有的话）
                try:
                    original_cell = target_ws[f"A{start_row}"]
                    new_cell = target_ws[f"A{final_row}"]
                    if original_cell.font:
                        new_cell.font = original_cell.font
                    if original_cell.alignment:
                        new_cell.alignment = original_cell.alignment
                    if original_cell.fill:
                        new_cell.fill = original_cell.fill
                except:
                    pass

        # 保存文件
        target_wb.save(output_path)
        print(f"💾 数据已成功写入文件")

        # 关闭工作簿
        source_wb.close()
        target_wb.close()
        
        result['success'] = True
        result['message'] = f"成功处理 {len(data_rows)} 行数据"
        result['rows_processed'] = len(data_rows)
        
        return result
        
    except Exception as e:
        result['message'] = f"处理过程中出现错误: {str(e)}"
        return result

def main():
    """主函数"""
    print("📊 Excel数据处理工具")
    print("=" * 60)
    print("功能：将源文件A-H列数据（除第一行）复制到模板文件中")
    print("起始位置：A7行")
    print("处理合并单元格：自动")
    print("=" * 60)
    print()
    
    print("📂 文件路径：")
    print(f"   源文件：{path_windows}")
    print(f"   模板：{muban_path}")
    print(f"   输出：{new_path}")
    print()
    
    # 检查文件是否存在
    if not os.path.exists(path_windows):
        print(f"❌ 错误：源文件不存在")
        print(f"   请检查路径：{path_windows}")
        return
    
    if not os.path.exists(muban_path):
        print(f"❌ 错误：模板文件不存在")
        print(f"   请检查路径：{muban_path}")
        return
    
    print("🔄 开始处理...")
    print()
    
    # 调用处理函数
    result = process_excel_files(path_windows, muban_path, new_path)
    
    print()
    if result['success']:
        print("🎉 处理成功！")
        print(f"📈 处理了 {result['rows_processed']} 行数据")
        print(f"💾 文件已保存到：{new_path}")
        print()
        print("✅ 完成！您可以打开输出文件查看结果。")
    else:
        print("❌ 处理失败！")
        print(f"错误信息：{result['message']}")
        print()
        print("请检查：")
        print("1. 文件路径是否正确")
        print("2. 文件是否被其他程序占用")
        print("3. 是否有足够的磁盘空间")

if __name__ == "__main__":
    main()
