"""
Excel数据处理工具 - 完整版本

功能：将源Excel文件的A-H列数据（除第一行）按模板格式复制到新文件中
起始位置：A7行，自动处理合并单元格

使用方法：
1. 修改下面三个路径变量
2. 运行脚本
"""

import openpyxl
from openpyxl.utils import get_column_letter
from openpyxl.styles import Alignment, Font, Border, Side, PatternFill
import os
import shutil

# ==================== 算法控制和路径设置 ====================

# 算法控制变量
a = 2  # 1=区县算法, 2=乡镇算法

# 配置文件路径
if a == 1:
    # 区县算法路径
    path_windows = r"C:\Users\<USER>\Desktop\新建文件夹\测试数据\建湖县\高标准农田项目建设基本底数（二）.xlsx"
    muban_path = r"C:\Users\<USER>\Desktop\新建文件夹\模板文件\区县表格.xlsx"
    new_path = r"C:\Users\<USER>\Desktop\新建文件夹\统计成果\建湖县\高标准农田项目建设基本底数（二）.xlsx"
elif a == 2:
    # 乡镇算法路径（暂时使用测试数据）
    path_windows = r"C:\Users\<USER>\Desktop\新建文件夹\测试数据\建湖县\高标准农田项目建设基本底数（二）.xlsx"
    muban_path = r"C:\Users\<USER>\Desktop\新建文件夹\模板文件\乡镇表格.xlsx"
    new_path = r"C:\Users\<USER>\Desktop\新建文件夹\统计成果\建湖县\分乡镇\高标准农田项目建设基本底数（二）.xlsx"

# ==================== 路径设置结束 ====================

def process_excel_files(source_path, template_path, output_path):
    """
    处理Excel文件的主函数
    
    Args:
        source_path: 源Excel文件路径
        template_path: 模板Excel文件路径  
        output_path: 输出Excel文件路径
    
    Returns:
        dict: 包含处理结果的字典
    """
    
    result = {
        'success': False,
        'message': '',
        'rows_processed': 0
    }
    
    try:
        # 验证输入文件
        if not os.path.exists(source_path):
            result['message'] = f"源文件不存在: {source_path}"
            return result
            
        if not os.path.exists(template_path):
            result['message'] = f"模板文件不存在: {template_path}"
            return result
        
        # 确保输出目录存在
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
            print(f"✓ 创建输出目录: {output_dir}")
        
        # 复制模板文件到新路径
        shutil.copy2(template_path, output_path)
        print(f"✓ 已复制模板文件到: {output_path}")
        
        # 读取源文件数据
        print(f"📖 正在读取源文件: {os.path.basename(source_path)}")
        source_wb = openpyxl.load_workbook(source_path, data_only=True)
        source_ws = source_wb.active

        # 获取源文件的工作表名称
        source_sheet_name = source_ws.title
        print(f"📋 源文件工作表名称: {source_sheet_name}")
        
        # 读取A-I列的数据（从第2行开始，跳过第1行）
        data_rows = []
        max_row = source_ws.max_row

        print(f"📊 源文件总行数: {max_row}")

        for row in range(2, max_row + 1):  # 从第2行开始
            row_data = []
            has_data = False

            for col in range(1, 10):  # A-I列 (1-9)
                cell = source_ws.cell(row=row, column=col)
                cell_value = cell.value
                row_data.append(cell_value)

                if cell_value is not None and str(cell_value).strip():
                    has_data = True

            # 只添加有数据的行
            if has_data:
                data_rows.append(row_data)
        
        print(f"📈 从源文件读取了 {len(data_rows)} 行有效数据")

        # 调试：打印前几行数据看看结构
        if data_rows and len(data_rows) > 0:
            print(f"🔍 调试 - 第一行数据: {data_rows[0]}")
            if len(data_rows) > 1:
                print(f"🔍 调试 - 第二行数据: {data_rows[1]}")

        # 创建市级代码到名称的映射
        city_code_map = {
            '3201': '南京市',
            '3202': '无锡市',
            '3203': '徐州市',
            '3204': '常州市',
            '3205': '苏州市',
            '3206': '南通市',
            '3207': '连云港市',
            '3208': '淮安市',
            '3209': '盐城市',
            '3210': '扬州市',
            '3211': '镇江市',
            '3212': '泰州市',
            '3213': '宿迁市'
        }

        # 获取区县名称和代码（从第一行数据中获取）
        county_name = "建湖县"  # 默认使用文件夹名称
        city_name = "盐城市"    # 默认值

        if data_rows and len(data_rows) > 0:
            first_row = data_rows[0]
            print(f"🔍 分析第一行数据，长度: {len(first_row)}")

            # 查找包含县名的列
            for i, cell_value in enumerate(first_row):
                if cell_value and isinstance(cell_value, str) and ('县' in cell_value or '市' in cell_value or '区' in cell_value):
                    county_name = cell_value
                    print(f"🏘️ 在第{i+1}列找到县名称: {county_name}")
                    break

            # 查找区县代码（通常是6位数字）
            for i, cell_value in enumerate(first_row):
                if cell_value and str(cell_value).isdigit() and len(str(cell_value)) >= 4:
                    county_code = str(cell_value)
                    city_code = county_code[:4]
                    if city_code in city_code_map:
                        city_name = city_code_map[city_code]
                        print(f"🏙️ 在第{i+1}列找到代码{county_code}，识别市名称: {city_name}")
                        break

        print(f"🏙️ 最终市名称: {city_name}")
        print(f"🏘️ 最终县名称: {county_name}")
        
        if not data_rows:
            result['message'] = "没有找到有效数据行"
            result['success'] = True
            source_wb.close()
            return result
        
        # 打开新文件（基于模板）
        target_wb = openpyxl.load_workbook(output_path)
        target_ws = target_wb.active

        # 将工作表名称改为源文件的名称
        target_ws.title = source_sheet_name
        print(f"📋 已将工作表名称设置为: {source_sheet_name}")

        # 修改A1标题
        new_title = f"{city_name}{county_name}2019-2025年高标准农田建设基本底数（二）"
        target_ws['A1'].value = new_title
        print(f"📝 已设置A1标题为: {new_title}")

        # 保存A7行的内容（A-AH的合并单元格内容）
        print("💾 保存A7行的提示内容...")
        start_row = 7
        a7_content = None
        a7_merged_range = None
        a7_original_font = None
        a7_original_alignment = None

        # 查找A7的合并单元格
        for merged_range in target_ws.merged_cells.ranges:
            if (merged_range.min_row == start_row and
                merged_range.min_col == 1):  # A7开始的合并单元格
                a7_merged_range = merged_range
                # 获取合并单元格的内容和样式
                a7_cell = target_ws.cell(row=start_row, column=1)
                a7_content = a7_cell.value
                a7_original_font = a7_cell.font
                a7_original_alignment = a7_cell.alignment
                print(f"📝 找到A7合并单元格: {merged_range}, 内容: {a7_content}")
                # 取消这个合并单元格
                target_ws.unmerge_cells(str(merged_range))
                print(f"✂️  临时取消A7合并单元格")

                # 清除A7行的内容和格式，避免影响数据
                for col in range(1, 35):  # A到AH列
                    cell = target_ws.cell(row=start_row, column=col)
                    cell.value = None
                    cell.font = Font()  # 重置字体
                    cell.alignment = Alignment()  # 重置对齐
                print(f"🧹 已清除A7行的内容和格式")
                break

        # 检查并处理其他可能冲突的合并单元格
        print("🔍 检查其他合并单元格...")
        merged_ranges = list(target_ws.merged_cells.ranges)
        ranges_to_unmerge = []

        end_row = start_row + len(data_rows) - 1

        for merged_range in merged_ranges:
            # 如果合并单元格与我们要填充的区域重叠（排除已处理的A7）
            if (merged_range.max_row >= start_row and
                merged_range.min_row <= end_row and
                merged_range.min_col <= 8 and
                merged_range.max_col >= 1):  # A-H列

                print(f"⚠️  发现冲突的合并单元格区域: {merged_range}")
                ranges_to_unmerge.append(merged_range)

        # 取消冲突的合并单元格
        for range_to_unmerge in ranges_to_unmerge:
            target_ws.unmerge_cells(str(range_to_unmerge))
            print(f"✂️  已取消合并单元格: {range_to_unmerge}")
        
        # 从A7开始填充数据
        print(f"📝 开始从第 {start_row} 行填充数据...")

        # 创建填充色和边框样式
        fill_color = PatternFill(start_color="E2EFDA", end_color="E2EFDA", fill_type="solid")
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        for i, row_data in enumerate(data_rows):
            current_row = start_row + i

            # 设置行高为20
            target_ws.row_dimensions[current_row].height = 20

            # 处理A-AH列（1-34列）
            for col_idx in range(1, 35):  # A到AH列
                col_letter = get_column_letter(col_idx)
                cell = target_ws[f"{col_letter}{current_row}"]

                # 如果是A-I列，设置数据值
                if col_idx <= 9 and col_idx - 1 < len(row_data):
                    value = row_data[col_idx - 1]
                    if value is not None:
                        cell.value = value

                # 为所有A-AH列设置统一格式
                try:
                    # 设置标准的数据格式
                    cell.font = Font(
                        name='宋体',
                        size=11,
                        bold=False
                    )
                    cell.alignment = Alignment(
                        horizontal='center',
                        vertical='center',
                        wrap_text=False
                    )

                    # 设置填充色
                    cell.fill = fill_color

                    # 设置内外边框
                    cell.border = thin_border

                except:
                    # 样式设置失败不影响主要功能
                    pass

        print(f"🎨 已为 {len(data_rows)} 行数据设置格式：行高20，A-AH列填充色#E2EFDA，内外边框")

        # 处理A6-H6的值计算
        print(f"🧮 开始计算A6-H6的汇总值...")

        # A6不变，保持原值
        a6_value = target_ws['A6'].value
        print(f"📌 A6保持原值: {a6_value}")

        # B6 = 数据条数
        b6_value = len(data_rows)
        target_ws['B6'].value = b6_value
        print(f"📊 B6设置为数据条数: {b6_value}")

        # 计算E6, F6, G6的求和（先转为数值再求和）
        def safe_sum_column(col_letter):
            """安全地对指定列进行求和，先转换为数值"""
            total = 0
            col_index = ord(col_letter) - ord('A')  # 转换为列索引

            for row_data in data_rows:
                if col_index < len(row_data):
                    value = row_data[col_index]
                    if value is not None:
                        try:
                            # 尝试转换为数值
                            if isinstance(value, str):
                                # 移除可能的空格和逗号
                                cleaned_value = value.replace(',', '').replace(' ', '')
                                if cleaned_value:
                                    numeric_value = float(cleaned_value)
                                else:
                                    numeric_value = 0
                            else:
                                numeric_value = float(value)
                            total += numeric_value
                        except (ValueError, TypeError):
                            # 如果无法转换为数值，跳过
                            pass
            return total

        # E6 = E列求和
        e6_value = safe_sum_column('E')
        target_ws['E6'].value = e6_value
        print(f"💰 E6设置为E列求和: {e6_value}")

        # F6 = F列求和
        f6_value = safe_sum_column('F')
        target_ws['F6'].value = f6_value
        print(f"💰 F6设置为F列求和: {f6_value}")

        # G6 = G列求和
        g6_value = safe_sum_column('G')
        target_ws['G6'].value = g6_value
        print(f"💰 G6设置为G列求和: {g6_value}")

        # H6 = G6/F6*100，保留一位小数
        if f6_value != 0:
            h6_value = round((g6_value / f6_value) * 100, 1)
        else:
            h6_value = 0.0
        target_ws['H6'].value = h6_value
        print(f"📈 H6设置为计算结果: {h6_value}% (G6/F6*100)")

        # I6 = I列求和
        i6_value = safe_sum_column('I')
        target_ws['I6'].value = i6_value
        print(f"💰 I6设置为I列求和: {i6_value}")

        # 为整个第6行设置加粗格式
        print(f"🎨 为第6行设置加粗格式...")
        for col_idx in range(1, 35):  # A到AH列
            col_letter = get_column_letter(col_idx)
            cell = target_ws[f"{col_letter}6"]

            # 保持原有字体属性，只修改加粗
            current_font = cell.font
            cell.font = Font(
                name=current_font.name or '宋体',
                size=current_font.size or 11,
                bold=True,  # 设置为加粗
                italic=current_font.italic,
                underline=current_font.underline,
                color=current_font.color
            )

        print(f"✅ A6-I6汇总值计算完成，第6行已设置加粗")
        
        # 将A7的内容放到数据的最后一行
        if a7_content is not None:
            final_row = start_row + len(data_rows)
            print(f"📌 将A7的提示内容移动到第 {final_row} 行...")

            # 在最后一行重新创建合并单元格并设置内容
            if a7_merged_range:
                # 计算新的合并范围（保持相同的列范围，但移动到最后）
                new_range = f"A{final_row}:AH{final_row}"
                target_ws.merge_cells(new_range)
                target_ws[f"A{final_row}"].value = a7_content
                print(f"✅ 已在第 {final_row} 行重新创建合并单元格: {new_range}")

                # 设置专门的格式：加粗、自动换行、行高77
                try:
                    new_cell = target_ws[f"A{final_row}"]

                    # 设置字体：加粗
                    new_cell.font = Font(
                        name='宋体',
                        size=11,
                        bold=True
                    )

                    # 设置对齐：自动换行
                    new_cell.alignment = Alignment(
                        horizontal='left',
                        vertical='top',
                        wrap_text=True  # 自动换行
                    )

                    # 设置行高为77
                    target_ws.row_dimensions[final_row].height = 77

                    print(f"🎨 已设置第 {final_row} 行格式：加粗、自动换行、行高77")

                except Exception as style_error:
                    print(f"⚠️  设置样式时出现问题: {style_error}")
                    pass

        # 保存文件
        target_wb.save(output_path)
        print(f"💾 数据已成功写入文件")

        # 关闭工作簿
        source_wb.close()
        target_wb.close()
        
        result['success'] = True
        result['message'] = f"成功处理 {len(data_rows)} 行数据"
        result['rows_processed'] = len(data_rows)
        
        return result
        
    except Exception as e:
        result['message'] = f"处理过程中出现错误: {str(e)}"
        return result

def process_single_township_sheet(source_ws, target_ws, township_name):
    """处理单个乡镇工作表"""
    # 读取A-I列的数据（从第2行开始，跳过第1行）
    data_rows = []
    max_row = source_ws.max_row

    for row in range(2, max_row + 1):  # 从第2行开始
        row_data = []
        has_data = False

        for col in range(1, 10):  # A-I列 (1-9)
            cell = source_ws.cell(row=row, column=col)
            cell_value = cell.value
            row_data.append(cell_value)

            if cell_value is not None and str(cell_value).strip():
                has_data = True

        # 只添加有数据的行
        if has_data:
            data_rows.append(row_data)

    if not data_rows:
        return 0

    # 创建市级代码到名称的映射
    city_code_map = {
        '3201': '南京市', '3202': '无锡市', '3203': '徐州市', '3204': '常州市',
        '3205': '苏州市', '3206': '南通市', '3207': '连云港市', '3208': '淮安市',
        '3209': '盐城市', '3210': '扬州市', '3211': '镇江市', '3212': '泰州市',
        '3213': '宿迁市'
    }

    # 获取区县名称和代码
    county_name = "建湖县"  # 默认值
    city_name = "盐城市"    # 默认值

    # 修改A1标题（包含乡镇名称）
    new_title = f"{city_name}{county_name}{township_name}2019-2025年高标准农田建设基本底数（二）"
    target_ws['A1'].value = new_title

    # 保存A7行的内容
    start_row = 7
    a7_content = None
    a7_merged_range = None

    # 查找A7的合并单元格
    for merged_range in target_ws.merged_cells.ranges:
        if (merged_range.min_row == start_row and merged_range.min_col == 1):
            a7_merged_range = merged_range
            a7_cell = target_ws.cell(row=start_row, column=1)
            a7_content = a7_cell.value
            target_ws.unmerge_cells(str(merged_range))

            # 清除A7行的内容和格式
            for col in range(1, 35):  # A到AH列
                cell = target_ws.cell(row=start_row, column=col)
                cell.value = None
                cell.font = Font()
                cell.alignment = Alignment()
            break

    # 检查其他合并单元格
    merged_ranges_to_remove = []
    for merged_range in target_ws.merged_cells.ranges:
        if merged_range.min_row >= start_row and merged_range.min_row < start_row + len(data_rows):
            merged_ranges_to_remove.append(merged_range)

    for merged_range in merged_ranges_to_remove:
        target_ws.unmerge_cells(str(merged_range))

    # 从A7开始填充数据（乡镇算法不需要填充色）
    for i, row_data in enumerate(data_rows):
        current_row = start_row + i
        target_ws.row_dimensions[current_row].height = 20

        # 处理A-AH列
        for col_idx in range(1, 35):  # A到AH列
            col_letter = get_column_letter(col_idx)
            cell = target_ws[f"{col_letter}{current_row}"]

            # 如果是A-I列，设置数据值
            if col_idx <= 9 and col_idx - 1 < len(row_data):
                value = row_data[col_idx - 1]
                if value is not None:
                    cell.value = value

            # 设置标准格式（乡镇算法不需要填充色）
            try:
                cell.font = Font(name='宋体', size=11, bold=False)
                cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=False)
                # 不设置填充色
            except:
                pass

    # 计算第6行的汇总值
    def safe_sum_column(col_letter):
        total = 0
        col_index = ord(col_letter) - ord('A')
        for row_data in data_rows:
            if col_index < len(row_data):
                value = row_data[col_index]
                if value is not None:
                    try:
                        if isinstance(value, str):
                            cleaned_value = value.replace(',', '').replace(' ', '')
                            numeric_value = float(cleaned_value) if cleaned_value else 0
                        else:
                            numeric_value = float(value)
                        total += numeric_value
                    except (ValueError, TypeError):
                        pass
        return total

    # 设置第6行的值
    target_ws['B6'].value = len(data_rows)  # 数据条数
    target_ws['E6'].value = safe_sum_column('E')  # E列求和
    target_ws['F6'].value = safe_sum_column('F')  # F列求和
    target_ws['G6'].value = safe_sum_column('G')  # G列求和
    target_ws['I6'].value = safe_sum_column('I')  # I列求和

    # H6 = G6/F6*100
    f6_value = safe_sum_column('F')
    g6_value = safe_sum_column('G')
    if f6_value != 0:
        h6_value = round((g6_value / f6_value) * 100, 1)
    else:
        h6_value = 0.0
    target_ws['H6'].value = h6_value

    # 为第6行设置加粗格式
    for col_idx in range(1, 35):  # A到AH列
        col_letter = get_column_letter(col_idx)
        cell = target_ws[f"{col_letter}6"]
        current_font = cell.font
        cell.font = Font(
            name=current_font.name or '宋体',
            size=current_font.size or 11,
            bold=True,
            italic=current_font.italic,
            underline=current_font.underline,
            color=current_font.color
        )

    # 将A7的内容放到数据的最后一行
    if a7_content is not None:
        final_row = start_row + len(data_rows)
        if a7_merged_range:
            new_range = f"A{final_row}:AH{final_row}"
            target_ws.merge_cells(new_range)
            target_ws[f"A{final_row}"].value = a7_content

            # 设置格式
            try:
                new_cell = target_ws[f"A{final_row}"]
                new_cell.font = Font(name='宋体', size=11, bold=True)
                new_cell.alignment = Alignment(horizontal='left', vertical='top', wrap_text=True)
                target_ws.row_dimensions[final_row].height = 77
            except:
                pass

    return len(data_rows)

def process_township_algorithm(source_path, template_path, output_path):
    """乡镇算法：处理多个工作表"""
    try:
        print(f"📖 正在读取源文件: {os.path.basename(source_path)}")
        source_wb = openpyxl.load_workbook(source_path, data_only=True)

        # 检查输出文件是否存在
        if os.path.exists(output_path):
            print(f"📂 输出文件已存在，将追加工作表: {output_path}")
            target_wb = openpyxl.load_workbook(output_path)
        else:
            # 创建输出目录
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)
                print(f"✓ 创建输出目录: {output_dir}")

            # 复制模板文件到输出位置
            shutil.copy2(template_path, output_path)
            print(f"✓ 已复制模板文件到: {output_path}")
            target_wb = openpyxl.load_workbook(output_path)

        total_processed = 0

        # 处理源文件中的每个工作表
        for sheet_name in source_wb.sheetnames:
            print(f"\n📋 处理工作表: {sheet_name}")
            source_ws = source_wb[sheet_name]

            # 检查目标文件中是否已存在同名工作表
            if sheet_name in target_wb.sheetnames:
                print(f"⚠️  工作表 '{sheet_name}' 已存在，跳过处理")
                continue

            # 创建新的工作表（复制模板的第一个工作表）
            template_ws = target_wb.worksheets[0]
            new_ws = target_wb.copy_worksheet(template_ws)
            new_ws.title = sheet_name

            # 处理这个工作表的数据
            rows_processed = process_single_township_sheet(source_ws, new_ws, sheet_name)
            total_processed += rows_processed

            print(f"✅ 工作表 '{sheet_name}' 处理完成，处理了 {rows_processed} 行数据")

        # 删除原始的模板工作表（如果存在且不是我们需要的）
        if len(target_wb.worksheets) > 1:
            template_sheet = target_wb.worksheets[0]
            if template_sheet.title not in source_wb.sheetnames:
                target_wb.remove(template_sheet)
                print(f"🗑️  已删除模板工作表: {template_sheet.title}")

        # 保存文件
        target_wb.save(output_path)
        target_wb.close()
        source_wb.close()

        return {
            'success': True,
            'message': '处理成功',
            'rows_processed': total_processed
        }

    except Exception as e:
        return {
            'success': False,
            'message': f"乡镇算法处理失败: {e}",
            'rows_processed': 0
        }

def main():
    """主函数"""
    print("📊 Excel数据处理工具")
    print("=" * 60)
    if a == 1:
        print("算法模式：区县算法")
        print("功能：将源文件A-I列数据（除第一行）复制到模板文件中")
    elif a == 2:
        print("算法模式：乡镇算法")
        print("功能：将源文件多个工作表的A-I列数据复制到模板文件中")
    print("起始位置：A7行")
    print("处理合并单元格：自动")
    print("=" * 60)
    print()

    print("📂 文件路径：")
    print(f"   源文件：{path_windows}")
    print(f"   模板：{muban_path}")
    print(f"   输出：{new_path}")
    print()

    # 检查文件是否存在
    if not os.path.exists(path_windows):
        print(f"❌ 错误：源文件不存在")
        print(f"   请检查路径：{path_windows}")
        return

    if not os.path.exists(muban_path):
        print(f"❌ 错误：模板文件不存在")
        print(f"   请检查路径：{muban_path}")
        return

    print("🔄 开始处理...")
    print()

    # 调用处理函数
    if a == 1:
        result = process_excel_files(path_windows, muban_path, new_path)
    elif a == 2:
        result = process_township_algorithm(path_windows, muban_path, new_path)
    else:
        result = {'success': False, 'message': f'无效的算法选择: a={a}', 'rows_processed': 0}
    
    print()
    if result['success']:
        print("🎉 处理成功！")
        print(f"📈 处理了 {result['rows_processed']} 行数据")
        print(f"💾 文件已保存到：{new_path}")
        print()
        print("✅ 完成！您可以打开输出文件查看结果。")
    else:
        print("❌ 处理失败！")
        print(f"错误信息：{result['message']}")
        print()
        print("请检查：")
        print("1. 文件路径是否正确")
        print("2. 文件是否被其他程序占用")
        print("3. 是否有足够的磁盘空间")

if __name__ == "__main__":
    main()
